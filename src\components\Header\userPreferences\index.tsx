import React, { useState, useEffect } from 'react';
import { Modal, Tabs, Form, Input, Button, Select, InputNumber, message, Table, Popconfirm } from 'antd';
import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import type { TabsProps } from 'antd';
import styles from './style.module.css';
import {
  createCustomChannel,
  setChannelType,
  setChannelWithParams,
  getCurrentChannelType,
  getAvailableChannelTypes,
  deleteCustomChannel,
  type CustomChannelData,
  type ChannelParamsData
} from './channelAPI';

const { TabPane } = Tabs;
const { Option } = Select;

interface UserPreferencesProps {
  visible: boolean;
  onClose: () => void;
}

interface ChannelListItem {
  key: string;
  channel_name: string;
  path_loss_exponent: number;
  shadowing_std: number;
  fading_factor: number;
  additional_loss: number;
  custom_params: any;
}

const UserPreferences: React.FC<UserPreferencesProps> = ({
  visible,
  onClose
}) => {
  const [activeTab, setActiveTab] = useState('customChannel');
  const [createChannelForm] = Form.useForm();
  const [setChannelForm] = Form.useForm();
  const [currentChannelType, setCurrentChannelType] = useState<string>('');
  const [availableChannelTypes, setAvailableChannelTypes] = useState<string[]>([]);
  const [customChannels, setCustomChannels] = useState<ChannelListItem[]>([]);
  const [loading, setLoading] = useState(false);

  // 获取当前信道类型和可用信道类型
  const fetchChannelInfo = async () => {
    try {
      const [currentType, availableTypes] = await Promise.all([
        getCurrentChannelType(),
        getAvailableChannelTypes()
      ]);
      
      if (currentType.success) {
        setCurrentChannelType(currentType.channel_type);
      }
      
      if (availableTypes.success) {
        setAvailableChannelTypes(availableTypes.channel_types);
        // 过滤出自定义信道（假设自定义信道不包含预设的基础信道类型）
        const basicChannels = ['free_space', 'two_ray_ground'];
        const customChannelNames = availableTypes.channel_types.filter(
          (type: string) => !basicChannels.includes(type)
        );
        // 这里需要额外的API来获取自定义信道的详细信息，暂时用名称创建列表
        const customChannelList = customChannelNames.map((name: string, index: number) => ({
          key: `${index}`,
          channel_name: name,
          path_loss_exponent: 2.0,
          shadowing_std: 0.0,
          fading_factor: 1.0,
          additional_loss: 0.0,
          custom_params: {}
        }));
        setCustomChannels(customChannelList);
      }
    } catch (error) {
      console.error('获取信道信息失败:', error);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchChannelInfo();
    }
  }, [visible]);

  // 创建自定义信道
  const handleCreateChannel = async (values: any) => {
    const channelData: CustomChannelData = {
      channel_name: values.channelName,
      path_loss_exponent: values.pathLossExponent || 2.0,
      shadowing_std: values.shadowingStd || 0.0,
      fading_factor: values.fadingFactor || 1.0,
      additional_loss: values.additionalLoss || 0.0,
      custom_params: {
        environment: values.environment || '',
        frequency: values.frequency || 0
      }
    };

    try {
      setLoading(true);
      const result = await createCustomChannel(channelData);
      
      if (result.success) {
        message.success(result.message || '自定义信道创建成功');
        createChannelForm.resetFields();
        fetchChannelInfo(); // 刷新信道列表
      } else {
        message.error(result.message || '创建失败');
      }
    } catch (error: any) {
      message.error(`创建失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 设置信道类型
  const handleSetChannelType = async (values: any) => {
    try {
      setLoading(true);
      let result;
      
      if (values.useAdvancedParams) {
        // 使用高级参数设置
        const paramsData: ChannelParamsData = {
          channel_type: values.channelType,
          path_loss_exponent: values.pathLossExponent,
          shadowing_std: values.shadowingStd,
          fading_factor: values.fadingFactor,
          additional_loss: values.additionalLoss,
          custom_params: {
            rain_attenuation: values.rainAttenuation || 0,
            atmospheric_loss: values.atmosphericLoss || 0
          }
        };
        result = await setChannelWithParams(paramsData);
      } else {
        // 基础设置
        result = await setChannelType(values.channelType);
      }
      
      if (result.success) {
        message.success(result.message || '信道类型设置成功');
        setCurrentChannelType(values.channelType);
        setChannelForm.resetFields();
      } else {
        message.error(result.message || '设置失败');
      }
    } catch (error: any) {
      message.error(`设置失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 删除自定义信道
  const handleDeleteChannel = async (channelName: string) => {
    try {
      setLoading(true);
      const result = await deleteCustomChannel(channelName);
      
      if (result.success) {
        message.success(result.message || '删除成功');
        fetchChannelInfo(); // 刷新信道列表
      } else {
        message.error(result.message || '删除失败');
      }
    } catch (error: any) {
      message.error(`删除失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 自定义信道表格列定义
  const channelColumns = [
    {
      title: '信道名称',
      dataIndex: 'channel_name',
      key: 'channel_name',
    },
    {
      title: '路径损耗指数',
      dataIndex: 'path_loss_exponent',
      key: 'path_loss_exponent',
    },
    {
      title: '阴影衰落标准差',
      dataIndex: 'shadowing_std',
      key: 'shadowing_std',
    },
    {
      title: '衰落因子',
      dataIndex: 'fading_factor',
      key: 'fading_factor',
    },
    {
      title: '额外损耗(dB)',
      dataIndex: 'additional_loss',
      key: 'additional_loss',
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: ChannelListItem) => (
        <div>
          <Popconfirm
            title="确定要删除这个自定义信道吗？"
            onConfirm={() => handleDeleteChannel(record.channel_name)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="link" 
              danger 
              icon={<DeleteOutlined />}
              size="small"
            >
              删除
            </Button>
          </Popconfirm>
        </div>
      ),
    },
  ];

  const handleClose = () => {
    createChannelForm.resetFields();
    setChannelForm.resetFields();
    onClose();
  };

  return (
    <Modal
      title="首选项"
      visible={visible}
      onCancel={handleClose}
      footer={null}
      width={800}
      centered
      className={styles.userPreferencesModal}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="自定义信道模型" key="customChannel">
          <div className={styles.tabContent}>
            {/* 创建自定义信道表单 */}
            <div className={styles.section}>
              <h3>创建自定义信道</h3>
              <Form
                form={createChannelForm}
                layout="vertical"
                onFinish={handleCreateChannel}
                className={styles.form}
              >
                <Form.Item
                  name="channelName"
                  label="信道名称"
                  rules={[{ required: true, message: '请输入信道名称' }]}
                >
                  <Input placeholder="请输入自定义信道名称" />
                </Form.Item>

                <div className={styles.formRow}>
                  <Form.Item
                    name="pathLossExponent"
                    label="路径损耗指数"
                    className={styles.formItem}
                  >
                    <InputNumber
                      min={1.0}
                      max={6.0}
                      step={0.1}
                      placeholder="默认: 2.0"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>

                  <Form.Item
                    name="shadowingStd"
                    label="阴影衰落标准差"
                    className={styles.formItem}
                  >
                    <InputNumber
                      min={0}
                      max={20}
                      step={0.1}
                      placeholder="默认: 0.0"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </div>

                <div className={styles.formRow}>
                  <Form.Item
                    name="fadingFactor"
                    label="衰落因子"
                    className={styles.formItem}
                  >
                    <InputNumber
                      min={0}
                      max={2}
                      step={0.01}
                      placeholder="默认: 1.0"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>

                  <Form.Item
                    name="additionalLoss"
                    label="额外损耗(dB)"
                    className={styles.formItem}
                  >
                    <InputNumber
                      min={0}
                      max={50}
                      step={0.1}
                      placeholder="默认: 0.0"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </div>

                <div className={styles.formRow}>
                  <Form.Item
                    name="environment"
                    label="环境类型"
                    className={styles.formItem}
                  >
                    <Select placeholder="选择环境类型">
                      <Option value="urban">城市</Option>
                      <Option value="rural">农村</Option>
                      <Option value="suburban">郊区</Option>
                      <Option value="satellite">卫星</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    name="frequency"
                    label="频率(Hz)"
                    className={styles.formItem}
                  >
                    <InputNumber
                      min={0}
                      step={1000000}
                      placeholder="例如: 2400000000"
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                </div>

                <Form.Item>
                  <Button 
                    type="primary" 
                    htmlType="submit" 
                    loading={loading}
                    className={styles.submitButton}
                  >
                    创建信道
                  </Button>
                </Form.Item>
              </Form>
            </div>

            {/* 已创建的自定义信道列表 */}
            <div className={styles.section}>
              <h3>已创建的自定义信道</h3>
              <Table
                columns={channelColumns}
                dataSource={customChannels}
                pagination={false}
                size="small"
                className={styles.channelTable}
              />
            </div>
          </div>
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default UserPreferences;
