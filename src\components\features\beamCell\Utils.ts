// @ts-nocheck

// 计算波束方向
export function calculateBeamOrientation(satToCylinder: any) {
  // 默认方向
  const defaultDirection = new Cesium.Cartesian3(0, 0, -1);

  // 计算旋转轴
  const rotationAxis = Cesium.Cartesian3.cross(
    defaultDirection,
    satToCylinder,
    new Cesium.Cartesian3()
  );

  if (Cesium.Cartesian3.magnitude(rotationAxis) < Cesium.Math.EPSILON6) {
    if (Cesium.Cartesian3.dot(defaultDirection, satToCylinder) > 0) {
      return Cesium.Quaternion.IDENTITY;
    } else {
      return Cesium.Quaternion.fromAxisAngle(Cesium.Cartesian3.UNIT_X, Math.PI);
    }
  }
  Cesium.Cartesian3.normalize(rotationAxis, rotationAxis);

  const rotationAngle = Math.acos(
    Cesium.Math.clamp(
      Cesium.Cartesian3.dot(defaultDirection, satToCylinder),
      -1.0,
      1.0
    )
  );
  return Cesium.Quaternion.fromAxisAngle(rotationAxis, rotationAngle);
}

export function calculateHexagonVertices(centerX: number, centerY: number, cellLength: number): Array<{x: number, y: number}> {
  const vertices = [];
  const radius = cellLength; // 对于正六边形，外接圆半径等于边长

  for (let i = 0; i < 6; i++) {
    const angle = (i * 60 + 30) * Math.PI / 180; // 每60度一个顶点
    const x = centerX + radius * Math.cos(angle);
    const y = centerY + radius * Math.sin(angle);
    vertices.push({ x, y });
  }

  return vertices;
}

export function offsetToGeographic(centerLon: number, centerLat: number, offsetX: number, offsetY: number): {longitude: number, latitude: number} {
  // 地球半径（米）
  const earthRadius = 6371000;

  // 将偏移量转换为角度
  const deltaLat = offsetY / earthRadius * (180 / Math.PI);
  const deltaLon = offsetX / (earthRadius * Math.cos(centerLat * Math.PI / 180)) * (180 / Math.PI);

  return {
    longitude: centerLon + deltaLon,
    latitude: centerLat + deltaLat
  };
}

// 小区中心点接口
export interface CellCenter {
  longitude: number;
  latitude: number;
  offsetX: number; // 相对于中心的偏移量（米）
  offsetY: number; // 相对于中心的偏移量（米）
}

// 波束信息接口
export interface BeamInfo {
  position: any; // Cesium.Cartesian3
  radius: number;
}

export function generateHexagonalCellCenters(
  centerLon: number,
  centerLat: number,
  cellLength: number,
  layers: number
): CellCenter[] {
  const cellCenters: CellCenter[] = [];

  // 中心小区
  cellCenters.push({
    longitude: centerLon,
    latitude: centerLat,
    offsetX: 0,
    offsetY: 0
  });

  // 六边形小区间距（中心到中心的距离）
  const cellSpacing = cellLength * Math.sqrt(3);

  // 生成各层小区
  for (let layer = 1; layer <= layers; layer++) {
    let x = -layer * cellSpacing / 2, y = -layer * cellSpacing * Math.sqrt(3) / 2;
    for(let angle = 0; angle <= 300; angle += 60){
      for (let step = 0; step < layer; step++) {
        const geographic = offsetToGeographic(centerLon, centerLat, x, y);
        cellCenters.push({
          longitude: geographic.longitude,
          latitude: geographic.latitude,
          offsetX: x,
          offsetY: y
        });
        const rad = angle / 180 * Math.PI;
        x += Math.cos(rad) * cellSpacing;
        y += Math.sin(rad) * cellSpacing;
      }
    }
  }

  return cellCenters;
}

export function calculateDistance(lon1: number, lat1: number, lon2: number, lat2: number): number {
  const R = 6371000; // 地球半径（米）
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}

export function findNearestCell(targetLon: number, targetLat: number, cellCenters: CellCenter[]): number {
  let minDistance = Infinity;
  let nearestIndex = 0;

  cellCenters.forEach((cell, index) => {
    const distance = calculateDistance(targetLon, targetLat, cell.longitude, cell.latitude);
    if (distance < minDistance) {
      minDistance = distance;
      nearestIndex = index;
    }
  });

  return nearestIndex;
}

export function createHexagonCesiumVertices(centerLon: number, centerLat: number, cellLength: number): any[] {
  const vertices = calculateHexagonVertices(0, 0, cellLength);
  const cesiumVertices: any[] = [];

  vertices.forEach(vertex => {
    const geographic = offsetToGeographic(centerLon, centerLat, vertex.x, vertex.y);
    cesiumVertices.push(Cesium.Cartesian3.fromDegrees(geographic.longitude, geographic.latitude));
  });

  return cesiumVertices;
}

export function calculateGroundProjection(satellitePosition: any): { longitude: number; latitude: number } {
  const cartographic = Cesium.Cartographic.fromCartesian(satellitePosition);
  return {
    longitude: Cesium.Math.toDegrees(cartographic.longitude),
    latitude: Cesium.Math.toDegrees(cartographic.latitude)
  };
}

export function calculateBeamGroundProjection(
  satellitePosition: any,
  beamCenterPosition: any,
  viewer: any
): { longitude: number; latitude: number } | null {
  try {
    // 最直接的方法：将波束中心点垂直投影到地球表面
    // 这相当于从波束中心点沿着地心方向投影到地面
    const ellipsoid = viewer.scene.globe.ellipsoid;
    const satToBeam = Cesium.Cartesian3.subtract(
      beamCenterPosition,
      satellitePosition,
      new Cesium.Cartesian3()
    );
    Cesium.Cartesian3.normalize(satToBeam, satToBeam);

    // 从卫星位置发射射线
    const ray = new Cesium.Ray(satellitePosition, satToBeam);
    const intersection = Cesium.IntersectionTests.rayEllipsoid(ray, ellipsoid);

    if (intersection) {
      // 取较近的交点（第一个与地面相交的点）
      const groundPos = Cesium.Ray.getPoint(ray, intersection.start);
      const cartographic = Cesium.Cartographic.fromCartesian(groundPos);

      const longitude = Cesium.Math.toDegrees(cartographic.longitude);
      const latitude = Cesium.Math.toDegrees(cartographic.latitude);

      return { longitude, latitude };
    }

    return null;
  } catch (error) {
    console.error('计算波束地面投影点失败:', error);
    return null;
  }
}