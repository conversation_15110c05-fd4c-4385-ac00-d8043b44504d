//@ts-nocheck
import { Dropdown, Menu } from "antd";
import { DownOutlined } from "@ant-design/icons";
import React, { useEffect, useState, useRef } from "react";
import useSimulationStore from '../../../store/simulationStore';
import "./satelliteInfo.css";
import { BaseStation, SetState } from "../types/type";
import { fetchSatelliteData, fetchGroundStationData, SatelliteData, GroundStationData, satelliteDataService } from '../../utils/satelliteData';
import { getVisibleSatellites, VisibleSatelliteResponse } from './visibleSatelliteService';
import { getSidelobe } from '../../Header/settingsatellite/beamConfigAPI';
import { getProcessingDelay } from './processingDelayService';
import AntennaPatternChart from './AntennaPatternChart';

type SatelliteInfoProp = {
  sateName: string;
  status?: string;
  type: string;
  curBaseStation?:BaseStation;
  weatherKey?:string;
  setCurBaseStation?: SetState<BaseStation>
  weatherIcon?:string;
  setWeatherIcon?:SetState<string>;
  satelliteId?: string;
  satelliteType?: string;
  workMode?: string;
  altitude?: string;
  beamCount?: number;
  antennaType?: string;
  antennaPattern?: string;
  stationId?: string;
  coordinates?: string;
  visibleSatellites?: number;
  constellationName?: string;
  groundStation?: string;
  connectedUsers?: number;
  antennaDirectionPattern?: string;
  antennaGain?: string;
};



const SatelliteInfo: React.FC<SatelliteInfoProp> = (props) => {
  var { 
    sateName, 
    status, 
    type, 
    curBaseStation, 
    setCurBaseStation, 
    weatherIcon, 
    setWeatherIcon,
    satelliteId = "SAT-001",
    satelliteType = "遥感卫星",
    workMode = "正常工作",
    altitude = "520km",
    beamCount = 48,
    antennaType = "相控阵天线",
    antennaPattern = "单波束",
    stationId = "GS-001",
    coordinates = "116.3°E, 39.9°N",
    visibleSatellites = 5,
    constellationName = "kuiper",
    groundStation = "北京地面站",
    connectedUsers = 128,
    antennaDirectionPattern = "多波束",
    antennaGain = "35dBi"
  } = props;

  const {
    simulationRunning,
    isLoading,
    currentSatelliteName,
    simulationConstellationName,
    pickedObjectId,
    pickedObjectName,
    cesiumTime,
    timeDiff,
    getNetworkModeConfig
   } = useSimulationStore();





  const [bgImg, setBgImg] = useState<string>("");
  const [dynamicSatelliteType, setDynamicSatelliteType] = useState<string>("遥感卫星");
  
  // 新增状态用于存储fetchSatelliteData返回的数据
  const [fetchedData, setFetchedData] = useState<string>("");
  const [objectType, setObjectType] = useState<'satellite' | 'groundstation' | null>(null);
  const [satelliteDataInfo, setSatelliteDataInfo] = useState<SatelliteData | null>(null);
  const [groundStationDataInfo, setGroundStationDataInfo] = useState<GroundStationData | null>(null);

  // 可见卫星状态
  const [visibleSatellite, setVisibleSatellite] = useState<string[]>([]);

  // 统一的显示数据对象
  const [showjson, setShowjson] = useState<Record<string, any>>({});

  // 定时器引用
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // 格式化数值的辅助函数
  const formatValue = (value: any): string => {
    if (value === null || value === undefined) {
      return '——';
    }

    if (typeof value === 'number') {
      // 如果是整数，直接返回
      if (Number.isInteger(value)) {
        return value.toString();
      }
      // 如果是小数，保留两位小数
      return value.toFixed(2);
    }

    // 如果是对象，格式化为键值对字符串
    if (typeof value === 'object' && value !== null) {
      if (Array.isArray(value)) {
        // 如果是数组，递归处理每个元素
        return value.map(item => formatValue(item)).join('\n');
      } else {
        // 如果是对象，转换为键值对格式，每个键值对换行.join(', ')
        return Object.entries(value)
          .map(([k, v]) => `${k}: ${formatValue(v)}`)
          .join('\n');
      }
    }

    return value.toString();
  };

  // 渲染信息项的函数
  const renderInfoItems = () => {
    const items: JSX.Element[] = [];

    Object.entries(showjson).forEach(([key, value], index) => {
      if (Array.isArray(value)) {
        // 处理数组
        const displayValue = value.length > 0
          ? value.map(item => formatValue(item)).join('\n')
          : '无';
        items.push(
          <div key={index} className="info-item">
            <div className="item-label">{key}</div>
            <div className="item-value" style={{
              whiteSpace: 'pre-line',
              lineHeight: '1.4'
            }}>{displayValue}</div>
          </div>
        );
      } else if (typeof value === 'object' && value !== null) {
        // 处理嵌套对象 - 格式化为多行显示
        const formattedObject = Object.entries(value)
          .map(([subKey, subValue]) => {
            let displaySubValue;
            if (Array.isArray(subValue)) {
              displaySubValue = subValue.length > 0
                ? subValue.map(item => formatValue(item)).join(', ')
                : '无';
            } else {
              displaySubValue = formatValue(subValue);
            }
            return `${subKey}: ${displaySubValue}`;
          })
          .join('\n');

        items.push(
          <div key={index} className="info-item">
            <div className="item-label">{key}</div>
            <div className="item-value" style={{
              whiteSpace: 'pre-line',
              lineHeight: '1.4'
            }}>
              {formattedObject}
            </div>
          </div>
        );
      } else {
        // 处理基本类型
        const displayValue = formatValue(value);
        items.push(
          <div key={index} className="info-item">
            <div className="item-label">{key}</div>
            <div className="item-value" style={{
              whiteSpace: 'pre-line',
              lineHeight: '1.4'
            }}>{displayValue}</div>
          </div>
        );
      }
    });

    // 将items按每行2个分组
    const rows = [];
    for (let i = 0; i < items.length; i += 2) {
      rows.push(
        <div key={i} className="info-row">
          {items[i]}
          {items[i + 1] || null}
        </div>
      );
    }
    return rows;
  };

  // console.log('卫星星系面板----------');
  

  useEffect(() => {
    // 首先判断是卫星还是地面站
    if (objectType === "groundstation") {
      setBgImg("BASESTATION.jpg");
    } else if (objectType === "satellite") {
      // 如果是卫星，继续执行原有的判断逻辑
      if (constellationName && constellationName.includes("starlink")) {
        setBgImg("STARLINK1.png");
      } else {
        setBgImg("01.png");
      }
    } else {
      // 默认情况（objectType 未设置时）
      setBgImg("BASESTATION.jpg");
    }

  }, [sateName, constellationName, objectType]);

  // 监听点击对象的变化，判断是卫星还是地面站并开始定时获取数据
  useEffect(() => {
    // 清除之前的定时器
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }


    // 重置状态
    setFetchedData("");
    setObjectType(null);
    setSatelliteDataInfo(null);
    setGroundStationDataInfo(null);

    if (!pickedObjectId || !pickedObjectName) {
      return;
    }

    console.log('检测到点击对象:', { id: pickedObjectId, name: pickedObjectName });

    // 判断是卫星还是地面站
    let isGroundStation = false;
    let isSatellite = false;
    let picktype = '';

    if (pickedObjectName.includes('Ground')) {
      isGroundStation = true;
      setObjectType('groundstation');
    } else if (pickedObjectName.includes('Satellite')) {
      isSatellite = true;
      setObjectType('satellite');
    }

    if (!isGroundStation && !isSatellite) {
      console.log('未识别的对象类型:', pickedObjectName);
      return;
    }

    // 定义数据获取函数
    const fetchData = async () => {
      try {
        // 获取最新的cesiumTime（在函数执行时获取，确保是最新值）
        const { cesiumTime: currentCesiumTime } = useSimulationStore.getState();
        
        if (isSatellite) {
          // console.log('获取卫星数据，ID:', pickedObjectId, '当前Cesium时间:', currentCesiumTime);
          const data = await fetchSatelliteData(pickedObjectId, currentCesiumTime);
          setSatelliteDataInfo(data);
          setFetchedData(data.status || '');
          setObjectType('satellite');

          // 获取处理延迟数据
          const processingDelay = await getProcessingDelay();

          const sidelobeData = await getSidelobe();
          // console.log('sidelobeData',sidelobeData)

          const combinedData = {
            manufacturer:simulationConstellationName,
            operator:simulationConstellationName,
            ...data,
            sidelobeData: sidelobeData,
            processingDelay: processingDelay*100
          };

          // 直接使用获取到的data填充showjson
          setShowjson(combinedData);

          // console.log('卫星数据获取成功:', data);
          // console.log('设置fetchedData为:', data.status || '');
        } else if (isGroundStation) {
          // console.log('获取地面站数据，ID:', pickedObjectId, '当前Cesium时间:', currentCesiumTime);
          const finalNumber: number = parseInt(pickedObjectId.split('/').pop() || '0', 10);
          const data = await fetchGroundStationData(finalNumber, currentCesiumTime);
          // console.log('获取地面站数据，ID:', finalNumber);

          // 获取可见卫星数据
          // 直接从store获取最新的timeDiff值
          const currentTimeDiff = useSimulationStore.getState().getTimeDiff();
          // console.log('当前最新的timeDiff值:', currentTimeDiff);
          // console.log('组件渲染时的timeDiff值:', currentCesiumTime);

          let currentVisibleSatellites: string[] = [];
          if (currentTimeDiff !== null) {
            try {
              const visibleSatelliteResult = await getVisibleSatellites(finalNumber, currentTimeDiff);
              if (visibleSatelliteResult.success && visibleSatelliteResult.data) {
                currentVisibleSatellites = visibleSatelliteResult.data.visible_satellites;
                setVisibleSatellite(currentVisibleSatellites);
                // console.log('可见卫星获取成功:', currentVisibleSatellites);
              } else {
                setVisibleSatellite([]);
                console.warn('可见卫星获取失败:', visibleSatelliteResult.message);
              }
            } catch (error) {
              console.error('获取可见卫星时出错:', error);
              setVisibleSatellite([]);
            }
          } else {
            console.warn('timeDiff为null，无法获取可见卫星');
            setVisibleSatellite([]);
          }

          setGroundStationDataInfo(data);
          setFetchedData(data.status || '');
          setObjectType('groundstation');

          // 获取网络模式配置并判断finalNumber是否等于position2
          const networkConfig = getNetworkModeConfig();
          let additionalData = {};

          if (networkConfig && networkConfig.terminals && networkConfig.groundStationLocation) {
            // 计算position2 (地面站位置在terminals数组中的索引)
            const position2 = networkConfig.terminals.indexOf(networkConfig.groundStationLocation);
            // console.log('地面站判断:', { finalNumber, position2, groundStationLocation: networkConfig.groundStationLocation, terminals: networkConfig.terminals });
            if (finalNumber === position2) {
              additionalData = { user_num: 1 };
              // console.log('finalNumber等于position2，添加user_num: 1');
            } else {
              additionalData = { user_num: 0 };
              // console.log('finalNumber不等于position2，添加user_num: 0');
            }
          }

          // 合并地面站数据和可见卫星数据
          const combinedData = {
            ...data,
            visible_satellites: currentVisibleSatellites,
            ...additionalData
          };
          setShowjson(combinedData);

          // console.log('地面站数据获取成功:', data);
          // console.log('设置fetchedData为:', data.status || '');
        }
      } catch (error) {
        // console.error('获取数据失败:', error);
        setFetchedData('数据获取失败');
      }
    };

    // 立即执行一次
    fetchData();

    // 设置定时器，每2秒执行一次
    intervalRef.current = setInterval(fetchData, satelliteDataService.UPDATE_FREQUENCY);

    console.log(`开始定时获取${isSatellite ? '卫星' : '地面站'}数据，间隔${satelliteDataService.UPDATE_FREQUENCY}ms`);

    // 清理函数
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
        console.log('停止定时获取数据');
      }
    };
  }, [pickedObjectId, pickedObjectName]); // 移除cesiumTime依赖，避免频繁重新执行

  // 调试：监控状态变化
  // useEffect(() => {
  //   console.log('satelliteDataInfo状态更新:', satelliteDataInfo);
  //   console.log('fetchedData状态更新:', fetchedData);
  // }, [satelliteDataInfo, fetchedData]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, []);
  // console.log('sateName----',sateName);




  if (!simulationRunning || isLoading) {
    return <div id="satellite-info" style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      color: '#666',
      fontSize: '14px'
    }}>
      请开始仿真并选择卫星/地面站
    </div>
  }


 
  return (
    <div id="satellite-info">
      <div
        id="sate-model"
        style={{ backgroundImage: `url(./images/${bgImg})` }}
      ></div>
      <div id="sate-info">
        <div id="up">
          <div className="name-item">
            {objectType === "satellite" ? "卫星名称" : "地面站名称"}
          </div>
          <div className="name-weather">
            <div className="name-sate">
              {/* {constellationName + " " + sateName} */}
              {pickedObjectName}
            </div>

          </div>
        </div>
        <div id="bottom">
          {/* 使用统一的渲染逻辑显示所有信息 */}
          {renderInfoItems()}
          {/* 天线方向图 - 只在卫星模式下显示，放在滚动区域内 */}
          <AntennaPatternChart objectType={objectType} />
        </div>
      </div>
    </div>
  );
};

export default SatelliteInfo;
