# 用户首选项组件

这个组件提供了用户首选项管理功能，目前主要包含自定义信道模型的管理。

## 功能特性

### 1. 自定义信道模型
- 创建自定义信道模型
- 查看已创建的自定义信道列表
- 删除自定义信道模型
- 支持多种信道参数配置

### 2. 信道设置
- 查看当前信道类型状态
- 设置信道类型（基础模式）
- 设置信道类型及参数（高级模式）
- 支持高级参数配置

## 组件结构

```
userPreferences/
├── index.tsx          # 主组件文件
├── channelAPI.ts      # API接口文件
├── style.module.css   # 样式文件
└── README.md          # 说明文档
```

## API接口

### 自定义信道模型相关

#### 创建自定义信道
- **接口**: `POST /api/beam/create_custom_channel`
- **参数**: 
  - `channel_name`: 信道名称（必填）
  - `path_loss_exponent`: 路径损耗指数（可选，默认2.0）
  - `shadowing_std`: 阴影衰落标准差（可选，默认0.0）
  - `fading_factor`: 衰落因子（可选，默认1.0）
  - `additional_loss`: 额外损耗（可选，默认0.0）
  - `custom_params`: 自定义参数（可选）

#### 删除自定义信道
- **接口**: `POST /api/beam/delete_custom_channel`
- **参数**: 
  - `channel_name`: 要删除的信道名称

### 信道设置相关

#### 设置信道类型（基础）
- **接口**: `POST /api/beam/set_channel_type`
- **参数**: 
  - `channel_type`: 信道类型名称

#### 设置信道类型及参数（高级）
- **接口**: `POST /api/beam/set_channel_with_params`
- **参数**: 
  - `channel_type`: 信道类型名称
  - `path_loss_exponent`: 路径损耗指数（可选）
  - `shadowing_std`: 阴影衰落标准差（可选）
  - `fading_factor`: 衰落因子（可选）
  - `additional_loss`: 额外损耗（可选）
  - `custom_params`: 自定义参数（可选）

#### 获取当前信道类型
- **接口**: `GET /api/beam/get_channel_type`

#### 获取可用信道类型列表
- **接口**: `GET /api/beam/get_available_channel_types`

## 使用方法

### 1. 在Header中添加按钮
组件已经集成到Header中，点击"首选项"按钮即可打开。

### 2. 创建自定义信道
1. 打开首选项面板
2. 选择"自定义信道模型"标签页
3. 填写信道参数
4. 点击"创建信道"按钮

### 3. 设置信道类型
1. 打开首选项面板
2. 选择"信道设置"标签页
3. 选择要设置的信道类型
4. 可选择是否使用高级参数
5. 点击"设置信道"按钮

## 参数说明

### 信道参数
- **路径损耗指数**: 控制信号随距离衰减的程度（1.0-6.0）
- **阴影衰落标准差**: 阴影衰落的标准差（0-20）
- **衰落因子**: 信号衰落的因子（0-2）
- **额外损耗**: 额外的信号损耗，单位dB（0-50）

### 环境类型
- **城市**: 城市环境
- **农村**: 农村环境
- **郊区**: 郊区环境
- **卫星**: 卫星通信环境

### 高级参数
- **雨衰减**: 由降雨引起的信号衰减（0-20dB）
- **大气损耗**: 大气引起的信号损耗（0-10dB）

## 错误处理

组件包含完整的错误处理机制：
- 参数验证
- API调用错误处理
- 用户友好的错误提示

## 样式特性

- 响应式设计，支持移动端
- 深色主题，与系统整体风格一致
- 平滑的动画效果
- 清晰的视觉层次

## 扩展性

组件设计具有良好的扩展性，可以轻松添加新的首选项类型：
1. 在主组件中添加新的TabPane
2. 创建对应的API接口
3. 添加相应的样式

## 注意事项

1. 确保后端API服务正常运行
2. 信道名称不能重复
3. 参数值需要在指定范围内
4. 删除信道前请确认不再使用
