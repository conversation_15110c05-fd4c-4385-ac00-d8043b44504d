import { notification } from 'antd';
import { basisBackendUrl, POST } from '../../utils/api/basicURL';

// 定义路由算法类型
type RouteAlgorithmKey = 'dijkstra' | 'floyd' | 'bfs';

// 算法中文名称映射
const ALGORITHM_NAME_MAPPING: Record<RouteAlgorithmKey, string> = {
  'dijkstra': 'Dijkstra (加权最短路径)',
  'floyd': 'Floyd-Warshall (所有对最短路径)',
  'bfs': '星地混合路由'
};

/**
 * 设置路由算法配置
 * @param algorithm 路由算法值 ('dijkstra', 'floyd', 'bfs')
 * @returns Promise<boolean> 设置是否成功
 */
export const setRouteAlgorithm = async (algorithm: string): Promise<boolean> => {
  try {
    // 类型检查
    const validAlgorithms: RouteAlgorithmKey[] = ['dijkstra', 'floyd', 'bfs'];
    if (!validAlgorithms.includes(algorithm as RouteAlgorithmKey)) {
      console.error('无效的路由算法选择:', algorithm);
      notification.error({
        message: '设置失败',
        description: '无效的路由算法选择'
      });
      return false;
    }
    
    const algorithmKey = algorithm as RouteAlgorithmKey;
    const algorithmName = ALGORITHM_NAME_MAPPING[algorithmKey];
    
    console.log(`设置路由算法: ${algorithmName} (${algorithmKey})`);

    // 构建API URL
    const apiUrl = basisBackendUrl + '/config/route_algorithm';
    
    // 构建请求数据，按照后端API要求的格式
    const requestData = {
      config_data: {
        path_algorithm: algorithmKey
      }
    };

    // 调用后端API
    const response = await POST(apiUrl, requestData);
    
    // console.log('路由算法设置成功:', response);
    notification.success({
      message: '设置成功',
      description: `路由算法已设置为: ${algorithmName}`
    });
    return true;
    
  } catch (error) {
    console.error('设置路由算法异常:', error);
    notification.error({
      message: '设置失败',
      description: '网络异常，请检查网络连接'
    });
    return false;
  }
};

/**
 * 获取路由算法的中文名称
 * @param algorithm 路由算法值
 * @returns 算法的中文名称
 */
export const getRouteAlgorithmName = (algorithm: string): string => {
  if (algorithm in ALGORITHM_NAME_MAPPING) {
    return ALGORITHM_NAME_MAPPING[algorithm as RouteAlgorithmKey];
  }
  return '未知算法';
};

/**
 * 检查是否为有效的路由算法
 * @param algorithm 路由算法值
 * @returns 是否有效
 */
export const isValidRouteAlgorithm = (algorithm: string): boolean => {
  const validAlgorithms: RouteAlgorithmKey[] = ['dijkstra', 'floyd', 'bfs'];
  return validAlgorithms.includes(algorithm as RouteAlgorithmKey);
}; 