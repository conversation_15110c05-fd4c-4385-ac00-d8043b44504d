import { Title } from "../Basic/basicElement.js";
import { But<PERSON> } from "../Basic/basicElement.js";
import { basisBackendUrl, GET } from "../../utils/api/basicURL";

/**
 * Satellite information display widget
 * Provides detailed satellite status information with auto-update functionality
 */
class SatelliteInfo {
    // Constants
    static UPDATE_FREQUENCY = 2000; // milliseconds
    static EARTH_RADIUS_KM = 6371;
    static METERS_TO_KM = 1000;
    static DEFAULT_ALTITUDE_RANGE = { min: 550, max: 1050 };
    static VELOCITY_RANGE = { min: -5, max: 5 };
    static POSITION_RANGE = { min: -10000, max: 10000 };
    static MOCK_GROUND_STATIONS = ['Tokyo', 'Beijing', 'Shanghai', 'New York', 'London', 'Sydney'];

    constructor() {
        this.element = this.createMainElement();
        this.currentSatelliteId = null;
        this.currentGroundStationId = null;
        this.updateInterval = null;
        this.updateFrequency = SatelliteInfo.UPDATE_FREQUENCY;
        this.isAutoUpdating = false;
        this.beamStatusData = {}; // 存储卫星波束状态数据
        
        // 初始化时加载波束状态
        this.loadBeamStatus();
    }

    /**
     * Creates the main widget element structure
     * @returns {HTMLElement} The main widget element
     */
    createMainElement() {
        const element = document.createElement('div');
        element.id = 'satelliteInfoSelector';
        element.className = 'frame-element';
        
        // Create title
        this.title = new Title('卫星/基站信息');
        element.appendChild(this.title.element);
        
        // Create content container
        this.contentContainer = this.createContentContainer();
        element.appendChild(this.contentContainer);
        
        // Create clear button
        this.clearButton = this.createClearButton();
        element.appendChild(this.clearButton.element);
        
        return element;
    }

    /**
     * Creates the scrollable content container
     * @returns {HTMLElement} The content container element
     */
    createContentContainer() {
        const container = document.createElement('div');
        container.className = 'frame-element-item';
        container.style.maxHeight = '220px';
        container.style.overflowY = 'auto';
        
        this.infoDisplay = document.createElement('div');
        this.infoDisplay.className = 'satellite-info-display';
        this.infoDisplay.innerHTML = '<div class="no-satellite-selected">等待选择卫星中</div>';
        container.appendChild(this.infoDisplay);
        
        return container;
    }

    /**
     * Creates the clear button with event handler
     * @returns {Button} The clear button instance
     */
    createClearButton() {
        const button = new Button('清除跟踪');
        button.element.addEventListener('click', () => this.clearInfo());
        return button;
    }

    /**
     * Displays satellite information in the widget
     * @param {Object} satelliteData - The satellite data object
     */
    displaySatelliteInfo(satelliteData) {
        if (this.shouldStartAutoUpdate(satelliteData.satellite_id)) {
            this.currentSatelliteId = satelliteData.satellite_id;
            this.currentGroundStationId = null; // Clear ground station tracking
            this.startAutoUpdate(satelliteData.satellite_id);
        }
        
        const infoHtml = this.generateInfoHtml(satelliteData);
        this.infoDisplay.innerHTML = infoHtml;
    }

    /**
     * Displays ground station information in the widget
     * @param {Object} groundStationData - Ground station data to display
     */
    displayGroundStationInfo(groundStationData) {
        if (this.shouldStartGroundStationAutoUpdate(groundStationData.ground_station_id)) {
            this.currentGroundStationId = groundStationData.ground_station_id;
            this.currentSatelliteId = null; // Clear satellite tracking
            this.startGroundStationAutoUpdate(groundStationData.ground_station_id);
        }
        
        const infoHtml = this.generateGroundStationHtml(groundStationData);
        this.infoDisplay.innerHTML = infoHtml;
    }

    /**
     * Checks if auto-update should be started for this satellite
     * @param {string} satelliteId - The satellite ID
     * @returns {boolean} True if auto-update should start
     */
    shouldStartAutoUpdate(satelliteId) {
        return this.currentSatelliteId !== satelliteId;
    }

    /**
     * Checks if auto-update should be started for this ground station
     * @param {string} groundStationId - The ground station ID
     * @returns {boolean} True if auto-update should start
     */
    shouldStartGroundStationAutoUpdate(groundStationId) {
        return this.currentGroundStationId !== groundStationId;
    }

    /**
     * Generates the HTML content for satellite information display
     * @param {Object} satelliteData - The satellite data object
     * @returns {string} HTML string for display
     */
    generateInfoHtml(satelliteData) {
        const isRealData = satelliteData.calculation_method === 'time_differential';
        
        return `
            ${this.createInfoItem('卫星ID', satelliteData.satellite_id)}
            ${this.createInfoItem('波束状态', this.getBeamStatusDisplay(satelliteData.satellite_id))}
            ${this.createPositionInfo(satelliteData.position)}
            ${this.createVelocityInfo(satelliteData.velocity)}
            ${this.createInfoItem('海拔高度', `${satelliteData.altitude.toFixed(2)} km`)}
            ${this.createListInfo('已连接地面基站', satelliteData.ground_stations)}
            ${this.createListInfo('邻接卫星', satelliteData.adjacent_satellites)}
            ${this.createStatusInfo(satelliteData.status)}
            ${this.createInfoItem('上次更新', satelliteData.last_updated)}
            
        `;
    }
    //${this.createAutoUpdateNotice()}
    //${this.createDataSourceNotice(isRealData)}
    /**
     * Generates the HTML content for ground station information display
     * @param {Object} groundStationData - The ground station data object
     * @returns {string} HTML string for display
     */
    generateGroundStationHtml(groundStationData) {
        return `
            ${this.createInfoItem('地面站 ID', groundStationData.ground_station_id)}
            <!--${this.createGroundStationPositionInfo(groundStationData.position)}-->
            ${this.createListInfo('连接卫星', groundStationData.connected_satellites)}
            ${this.createGroundStationBeamInfo(groundStationData)}
            ${this.createBeamConnectionsInfo(groundStationData.beam_connections)}
            ${this.createInfoItem('已连接', groundStationData.active_connections)}
            ${this.createStatusInfo(groundStationData.status)}
            ${this.createInfoItem('上次更新', groundStationData.last_updated)}
            
        `;
    }
    //${this.createAutoUpdateNotice()}
    /**
     * Creates a standard info item HTML
     * @param {string} label - The label text
     * @param {string} value - The value text
     * @returns {string} HTML string for the info item
     */
    createInfoItem(label, value) {
        return `<div class="satellite-info-item"><strong>${label}:</strong> ${value}</div>`;
    }

    /**
     * Creates position information HTML
     * @param {Object} position - Position object with x, y, z coordinates
     * @returns {string} HTML string for position info
     */
    createPositionInfo(position) {
        return `
            <div class="satellite-info-item">
                <strong>空间位置 (X, Y, Z):</strong><br>
                X: ${position.x.toFixed(2)} km<br>
                Y: ${position.y.toFixed(2)} km<br>
                Z: ${position.z.toFixed(2)} km
            </div>
        `;
    }

    /**
     * Creates velocity information HTML
     * @param {Object} velocity - Velocity object with x, y, z components
     * @returns {string} HTML string for velocity info
     */
    createVelocityInfo(velocity) {
        return `
            <div class="satellite-info-item">
                <strong>空间速度 (X, Y, Z):</strong><br>
                X: ${velocity.x.toFixed(2)} km/s<br>
                Y: ${velocity.y.toFixed(2)} km/s<br>
                Z: ${velocity.z.toFixed(2)} km/s
            </div>
        `;
    }

    /**
     * Creates list information HTML (for ground stations and adjacent satellites)
     * @param {string} label - The label text
     * @param {Array} items - Array of items to display
     * @returns {string} HTML string for list info
     */
    createListInfo(label, items) {
        const content = items.length > 0 ? 
            items.map(item => `<div>• ${item}</div>`).join('') : 
            '<div>• None</div>';
        
        return `
            <div class="satellite-info-item">
                <strong>${label}:</strong>
                ${content}
            </div>
        `;
    }



    /**
     * Creates ground station position information HTML
     * @param {Object} position - Position object with latitude, longitude, altitude
     * @returns {string} HTML string for position info
     */
    createGroundStationPositionInfo(position) {
        return `
            <div class="satellite-info-item">
                <strong>地面站位置:</strong><br>
                Latitude: ${position.latitude?.toFixed(6) || 'N/A'}°<br>
                Longitude: ${position.longitude?.toFixed(6) || 'N/A'}°<br>
                Altitude: ${position.altitude?.toFixed(2) || 'N/A'} m
            </div>
        `;
    }

    /**
     * Creates ground station beam information HTML
     * @param {Object} groundStationData - Ground station data object
     * @returns {string} HTML string for beam info
     */
    createGroundStationBeamInfo(groundStationData) {
        const utilizationPercentage = groundStationData.total_capacity > 0 ? 
            ((groundStationData.used_capacity / groundStationData.total_capacity) * 100).toFixed(1) : 0;
            
        return `
            <div class="satellite-info-item">
                <strong>通信参数:</strong><br>
                总容量: ${(groundStationData.total_capacity / 1e6).toFixed(2)} Mbps<br>
                已使用容量: ${(groundStationData.used_capacity / 1e6).toFixed(2)} Mbps<br>
                利用率: ${utilizationPercentage}%
            </div>
        `;
    }

    /**
     * Creates beam connections information HTML
     * @param {Array} beamConnections - Array of beam connection objects
     * @returns {string} HTML string for beam connections info
     */
    createBeamConnectionsInfo(beamConnections) {
        if (!Array.isArray(beamConnections) || beamConnections.length === 0) {
            return `
                <div class="satellite-info-item">
                    <strong>波束连接:</strong><br>
                    无波束连接
                </div>
            `;
        }
        
        const connectionsHtml = beamConnections.map(conn => `
            <div style="margin-left: 15px; border-left: 2px solid #ddd; padding-left: 10px; margin-bottom: 10px;">
                <strong>卫星 ${conn.satellite_id}:</strong><br>
                容量: ${(conn.capacity/1e6).toFixed(2)} Mbps<br>
                SINR: ${conn.sinr.toFixed(2)} dB<br>
                信道增益: ${conn.channel_gain.toFixed(2)} dB<br>
                天线增益: ${conn.antenna_gain.toFixed(2)} dB
            </div>
        `).join('');
        
        return `
            <div class="satellite-info-item">
                <strong>波束连接： (${beamConnections.length}):</strong><br>
                ${connectionsHtml}
            </div>
        `;
    }

    /**
     * Creates status information HTML with styling
     * @param {string} status - The satellite status
     * @returns {string} HTML string for status info
     */
    createStatusInfo(status) {
        return `
            <div class="satellite-info-item">
                <strong>状态:</strong> <span class="status-${status.toLowerCase()}">${status}</span>
            </div>
        `;
    }

    /**
     * Creates auto-update notice HTML
     * @returns {string} HTML string for auto-update notice
     */
    createAutoUpdateNotice() {
        if (!this.isAutoUpdating) return '';
        
        return `
            <div class="satellite-info-item auto-update-notice">
                <em>🔄 Auto-updating every ${this.updateFrequency / 1000}s</em>
            </div>
        `;
    }

    /**
     * Creates data source notice HTML
     * @param {boolean} isRealData - Whether this is real data or mock data
     * @returns {string} HTML string for data source notice
     */
    createDataSourceNotice(isRealData) {
        if (isRealData) {
            return `
                <div class="satellite-info-item real-data-notice">
                    <em>✅ Real-time data calculated using orbital mechanics</em>
                </div>
            `;
        }
        
        return `
            <div class="satellite-info-item mock-data-notice">
                <em>⚠️ Note: Currently displaying mock data for demo purposes</em>
            </div>
        `;
    }

    /**
     * 加载卫星波束状态数据（仅在初始化时调用一次）
     */
    async loadBeamStatus() {
        try {
            console.log('🚀 加载卫星波束状态数据...');
            const data = await this.fetchBeamStatus();
            
            // 检查数据格式并正确解析
            if (data && data.beam_status) {
                // 如果 beam_status 是字符串，需要解析JSON
                if (typeof data.beam_status === 'string') {
                    this.beamStatusData = JSON.parse(data.beam_status);
                } else {
                    this.beamStatusData = data.beam_status;
                }
            } else {
                // 如果数据直接是期望的格式
                this.beamStatusData = data || {};
            }
            
            //console.log('✓ 波束状态数据加载完成:', this.beamStatusData);
            console.log('✓ 波束状态数据条目数:', Object.keys(this.beamStatusData).length);
        } catch (error) {
            console.error('加载波束状态失败，使用默认状态:', error);
            // 如果加载失败，使用空对象，getBeamStatusDisplay 会返回默认值
            this.beamStatusData = {};
        }
    }

    /**
     * 从后端API获取波束状态数据
     * @returns {Promise<Object>} 波束状态数据对象 {satelliteId: status}
     */
    async fetchBeamStatus() {
        try {
            const response = await fetch(`${basisBackendUrl}/get_beam_status`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.status !== 200 || !data.data) {
                throw new Error(data.message || 'Failed to get beam status');
            }

            return data.data;
        } catch (error) {
            console.error('API获取波束状态失败，生成模拟数据:', error);
            // 返回模拟数据用于演示
            return this.generateMockBeamStatus();
        }
    }

    /**
     * 生成模拟波束状态数据
     * @returns {Object} 模拟的波束状态数据
     */
    generateMockBeamStatus() {
        return {
            '1': 'activate',
            '2': 'deactivate', 
            '3': 'activate',
            '4': 'activate',
            '5': 'activate',
            '6': 'deactivate'
        };
    }

    /**
     * 获取卫星波束状态的显示文本
     * @param {string} satelliteId - 卫星ID
     * @returns {string} 波束状态显示文本（包含样式）
     */
    getBeamStatusDisplay(satelliteId) {
        // 提取卫星ID中的数字部分
        const satNumber = satelliteId.toString();
        const status = this.beamStatusData[satNumber];
        //console.log(`卫星 ${satelliteId} 的波束状态:`, status);
        
        if (status === 'activate') {
            return '<span class="beam-status-active">启用</span>';
        } else if (status === 'deactivate') {
            return '<span class="beam-status-inactive">不启用</span>';
        } else {
            // 默认状态为启用
            return '<span class="beam-status-active">启用</span>';
        }
    }

    /**
     * 手动刷新波束状态数据
     * @returns {Promise<void>}
     */
    async refreshBeamStatus() {
        console.log('🔄 手动刷新波束状态数据...');
        await this.loadBeamStatus();
        
        // 如果当前正在显示卫星信息，重新刷新显示
        if (this.currentSatelliteId) {
            try {
                const satelliteData = await this.fetchSatelliteData(this.currentSatelliteId);
                if (satelliteData) {
                    const infoHtml = this.generateInfoHtml(satelliteData);
                    this.infoDisplay.innerHTML = infoHtml;
                    console.log('✓ 卫星信息已更新波束状态');
                }
            } catch (error) {
                console.error('刷新卫星信息时出错:', error);
            }
        }
    }

    /**
     * Clears the information display
     */
    clearInfo() {
        this.stopAutoUpdate();
        this.currentSatelliteId = null;
        this.currentGroundStationId = null;
        this.infoDisplay.innerHTML = '<div class="no-satellite-selected">等待选择中</div>';
    }

    /**
     * Starts auto-updating satellite data
     * @param {string} satelliteId - The satellite ID to update
     */
    startAutoUpdate(satelliteId) {
        this.stopAutoUpdate();
        
        this.isAutoUpdating = true;
        this.updateInterval = setInterval(async () => {
            await this.handleAutoUpdate(satelliteId);
        }, this.updateFrequency);
        
        console.log(`✓ Auto-update started for satellite ${satelliteId} (every ${this.updateFrequency / 1000}s)`);
    }

    /**
     * Handles a single auto-update cycle
     * @param {string} satelliteId - The satellite ID to update
     */
    async handleAutoUpdate(satelliteId) {
        if (this.currentSatelliteId !== satelliteId || !this.isAutoUpdating) {
            return;
        }
        
        console.log(`🔄 Auto-updating satellite data for ${satelliteId}`);
        
        try {
            const satelliteData = await this.fetchSatelliteData(satelliteId);
            if (satelliteData && this.currentSatelliteId === satelliteId) {
                this.displaySatelliteInfo(satelliteData);
            }
        } catch (error) {
            console.error('Error during auto-update:', error);
        }
    }

    /**
     * Stops auto-updating satellite data
     */
    stopAutoUpdate() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
            this.isAutoUpdating = false;
            console.log('✓ Auto-update stopped');
        }
    }

    /**
     * Starts auto-update for ground station information
     * @param {string} groundStationId - The ground station ID to track
     */
    startGroundStationAutoUpdate(groundStationId) {
        this.stopAutoUpdate(); // Stop any existing updates
        
        this.updateInterval = setInterval(async () => {
            await this.handleGroundStationAutoUpdate(groundStationId);
        }, this.updateFrequency);
        
        this.isAutoUpdating = true;
        console.log(`✓ Auto-update started for ground station ${groundStationId}`);
    }

    /**
     * Handles the periodic auto-update for ground station
     * @param {string} groundStationId - The ground station ID to update
     */
    async handleGroundStationAutoUpdate(groundStationId) {
        try {
            // Only update if we're still tracking this ground station
            if (this.currentGroundStationId === groundStationId) {
                const groundStationData = await this.fetchGroundStationData(groundStationId);
                const infoHtml = this.generateGroundStationHtml(groundStationData);
                this.infoDisplay.innerHTML = infoHtml;
            }
        } catch (error) {
            console.warn(`Failed to update ground station ${groundStationId}:`, error);
            // Continue trying - don't stop the auto-update on temporary failures
        }
    }

    /**
     * Fetches satellite data from backend API or returns mock data
     * @param {string} satelliteId - The satellite ID to fetch data for
     * @returns {Promise<Object>} Satellite data object
     */
    async fetchSatelliteData(satelliteId) {
        try {
            //console.log("The satelliteId is: " + satelliteId)
            const backendData = await this.callBackendAPI(satelliteId);
            return this.transformBackendData(backendData, satelliteId);
        } catch (error) {
            console.error('Error fetching satellite data:', error);
            return this.generateMockData(satelliteId);
        }
    }

    /**
     * Fetches ground station data from backend API or returns mock data
     * @param {string} groundStationId - The ground station ID to fetch data for
     * @returns {Promise<Object>} Ground station data object
     */
    async fetchGroundStationData(groundStationId) {
        try {
            const backendData = await this.callGroundStationBackendAPI(groundStationId);
            return this.transformGroundStationData(backendData, groundStationId);
        } catch (error) {
            console.error('Error fetching ground station data:', error);
            return this.generateMockGroundStationData(groundStationId);
        }
    }

    /**
     * Calls the backend API for satellite status
     * @param {string} satelliteId - The satellite ID
     * @returns {Promise<Object>} Backend response data
     */
    async callBackendAPI(satelliteId) {
        //const satelliteIdNum = this.extractSatelliteNumber(satelliteId);
        const currentTime = this.getCurrentTime();
        
        const response = await fetch(`${basisBackendUrl}/get_satellite_status`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                satellite_id: satelliteId,
                time: currentTime
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.status !== 200 || !data.data) {
            throw new Error(data.message || 'Failed to get satellite status');
        }

        return data;
    }

    /**
     * Calls the backend API for ground station status
     * @param {string} groundStationId - The ground station ID
     * @returns {Promise<Object>} Backend response data
     */
    async callGroundStationBackendAPI(groundStationId) {
        const groundStationIdNum = parseInt(groundStationId);
        const currentTime = this.getCurrentTime();
        
        const response = await fetch(`${basisBackendUrl}/get_ground_station_status`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                ground_station_id: groundStationIdNum,
                time: currentTime
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.status !== 200 || !data.data) {
            throw new Error(data.message || 'Failed to get ground station status');
        }

        return data;
    }

    // /**
    //  * Extracts satellite number from satellite ID string
    //  * @param {string} satelliteId - The full satellite ID
    //  * @returns {number} The satellite number
    //  */
    // extractSatelliteNumber(satelliteId) {
    //     const satNumber = satelliteId.split(' ').pop() || '0';
    //     return parseInt(satNumber);
    // }

    /**
     * Gets current time from Cesium viewer or system time
     * @returns {string} Current time string
     */
    getCurrentTime() {
        if (window.viewer && window.viewer.clock) {
            // Use Cesium's simulation time to respect time controls (play/pause/speed)
            // This allows users to control satellite tracking through Cesium's time widget
            const cesiumTime = window.viewer.clock.currentTime.toString();
            
            // Debug info about Cesium clock state
            //console.log(`🕐 Cesium time: ${cesiumTime}, shouldAnimate: ${window.viewer.clock.shouldAnimate}, multiplier: ${window.viewer.clock.multiplier}`);
            
            return cesiumTime;
        }
        // Fallback to system time if Cesium viewer is not available
        console.log('⚠️ Cesium viewer not available, using system time');
        return new Date().toISOString();
    }

    /**
     * Transforms backend data to frontend format
     * @param {Object} backendData - Raw backend response
     * @param {string} satelliteId - The satellite ID
     * @returns {Object} Transformed satellite data
     */
    transformBackendData(backendData, satelliteId) {
        const data = backendData.data;
        
        return {
            satellite_id: satelliteId,
            position: this.convertPosition(data.position),
            velocity: this.convertVelocity(data.velocity),
            altitude: this.calculateAltitude(data.position),
            ground_stations: this.formatConnectedGroundStations(data.connected_ground_stations || []),
            adjacent_satellites: this.formatAdjacentSatellites(data.adjacent_satellites || []),
            status: '实时数据',
            last_updated: new Date().toLocaleString(),
            calculation_method: data.calculation_method || 'unknown'
        };
    }

    /**
     * Transforms ground station backend data to frontend format
     * @param {Object} backendData - Raw backend response
     * @param {string} groundStationId - The ground station ID
     * @returns {Object} Transformed ground station data
     */
    transformGroundStationData(backendData, groundStationId) {
        const data = backendData.data;
        
        return {
            ground_station_id: groundStationId,
            position: data.position || { latitude: 0, longitude: 0, altitude: 0 },
            connected_satellites: data.connected_satellites || [],
            beam_connections: data.beam_connections || [],
            total_capacity: data.total_capacity || 0,
            used_capacity: data.used_capacity || 0,
            active_connections: data.active_connections || 0,
            status: '实时数据',
            last_updated: new Date().toLocaleString()
        };
    }
    /**
     * Converts position from meters to kilometers
     * @param {Object} position - Position in meters
     * @returns {Object} Position in kilometers
     */
    convertPosition(position) {
        return {
            x: position.x / SatelliteInfo.METERS_TO_KM,
            y: position.y / SatelliteInfo.METERS_TO_KM,
            z: position.z / SatelliteInfo.METERS_TO_KM
        };
    }

    /**
     * Converts velocity from m/s to km/s
     * @param {Object} velocity - Velocity in m/s
     * @returns {Object} Velocity in km/s
     */
    convertVelocity(velocity) {
        return {
            x: (velocity?.x || 0) / SatelliteInfo.METERS_TO_KM,
            y: (velocity?.y || 0) / SatelliteInfo.METERS_TO_KM,
            z: (velocity?.z || 0) / SatelliteInfo.METERS_TO_KM
        };
    }

    /**
     * Calculates altitude from position vector
     * @param {Object} position - Position in meters
     * @returns {number} Altitude in kilometers
     */
    calculateAltitude(position) {
        const magnitude = Math.sqrt(
            Math.pow(position.x, 2) + 
            Math.pow(position.y, 2) + 
            Math.pow(position.z, 2)
        );
        
        return magnitude / SatelliteInfo.METERS_TO_KM - SatelliteInfo.EARTH_RADIUS_KM;
    }

    /**
     * Formats adjacent satellites array for display
     * @param {Array} adjacentSatellites - Array of adjacent satellite IDs
     * @returns {Array} Formatted array of satellite names
     */
    formatAdjacentSatellites(adjacentSatellites) {
        if (!Array.isArray(adjacentSatellites)) {
            console.log('adjacentSatellites is not an array');
            return [];
        }
        
        return adjacentSatellites.map(satId => `Satellite ${satId}`);
    }

    /**
     * Formats connected ground stations array for display
     * @param {Array} connectedGroundStations - Array of connected ground station IDs
     * @returns {Array} Formatted array of ground station names
     */
    formatConnectedGroundStations(connectedGroundStations) {
        if (!Array.isArray(connectedGroundStations)) {
            console.log('connectedGroundStations is not an array');
            return [];
        }
        
        return connectedGroundStations.map(gsId => `Ground Station ${gsId}`);
    }

    /**
     * Generates mock satellite data for demonstration purposes
     * @param {string} satelliteId - The satellite ID
     * @returns {Object} Mock satellite data
     */
    generateMockData(satelliteId) {
       
        const satNumber = satelliteId
        const seed = this.hashCode(satelliteId);
        const rng = this.seededRandom(seed);
        
        return {
            satellite_id: satelliteId,
            position: this.generateMockPosition(rng),
            velocity: this.generateMockVelocity(rng),
            altitude: this.generateMockAltitude(rng),
            ground_stations: this.getMockGroundStations(rng),
            adjacent_satellites: this.getMockAdjacentSatellites(satNumber, rng),
            status: '模拟数据',
            last_updated: new Date().toLocaleString()
        };
    }

    /**
     * Generates mock ground station data for demonstration purposes
     * @param {string} groundStationId - The ground station ID
     * @returns {Object} Mock ground station data
     */
    generateMockGroundStationData(groundStationId) {
        const seed = this.hashCode(`gs_${groundStationId}`);
        const rng = this.seededRandom(seed);
        
        // Generate realistic ground station coordinates
        const latitude = (rng() - 0.5) * 160; // -80 to 80 degrees
        const longitude = (rng() - 0.5) * 360; // -180 to 180 degrees
        const altitude = rng() * 1000 + 100; // 100-1100 meters
        
        const connectedSatellites = [];
        const numConnections = Math.floor(rng() * 5) + 1; // 1-5 connections
        for (let i = 0; i < numConnections; i++) {
            connectedSatellites.push(`Satellite ${Math.floor(rng() * 1000) + 1}`);
        }
        
        const totalCapacity = Math.floor(rng() * 5000) + 1000; // 1000-6000 Mbps
        const usedCapacity = Math.floor(rng() * totalCapacity * 0.8); // Up to 80% utilization
        
        return {
            ground_station_id: groundStationId,
            position: {
                latitude: latitude,
                longitude: longitude,
                altitude: altitude
            },
            connected_satellites: connectedSatellites,
            beam_connections: [],
            total_capacity: totalCapacity,
            used_capacity: usedCapacity,
            active_connections: connectedSatellites.length,
            status: '模拟数据',
            last_updated: new Date().toLocaleString()
        };
    }

    /**
     * Generates mock position data
     * @param {Function} rng - Random number generator
     * @returns {Object} Mock position
     */
    generateMockPosition(rng) {
        const range = SatelliteInfo.POSITION_RANGE;
        return {
            x: (rng() - 0.5) * (range.max - range.min),
            y: (rng() - 0.5) * (range.max - range.min),
            z: (rng() - 0.5) * (range.max - range.min)
        };
    }

    /**
     * Generates mock velocity data
     * @param {Function} rng - Random number generator
     * @returns {Object} Mock velocity
     */
    generateMockVelocity(rng) {
        const range = SatelliteInfo.VELOCITY_RANGE;
        return {
            x: (rng() - 0.5) * (range.max - range.min),
            y: (rng() - 0.5) * (range.max - range.min),
            z: (rng() - 0.5) * (range.max - range.min)
        };
    }

    /**
     * Generates mock altitude data
     * @param {Function} rng - Random number generator
     * @returns {number} Mock altitude in km
     */
    generateMockAltitude(rng) {
        const range = SatelliteInfo.DEFAULT_ALTITUDE_RANGE;
        return range.min + rng() * (range.max - range.min);
    }



    /**
     * Generates a hash code from a string for consistent seeding
     * @param {string} str - Input string
     * @returns {number} Hash code
     */
    hashCode(str) {
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        return Math.abs(hash);
    }

    /**
     * Creates a seeded random number generator
     * @param {number} seed - The seed value
     * @returns {Function} Random number generator function
     */
    seededRandom(seed) {
        let current = seed;
        return function() {
            current = (current * 9301 + 49297) % 233280;
            return current / 233280;
        };
    }

    /**
     * Gets mock ground stations for a satellite
     * @param {Function} rng - Random number generator
     * @returns {Array<string>} Array of ground station names
     */
    getMockGroundStations(rng) {
        const stations = SatelliteInfo.MOCK_GROUND_STATIONS;
        const count = Math.floor(rng() * 3); // 0-2 ground stations
        const result = [];
        
        for (let i = 0; i < count; i++) {
            const station = stations[Math.floor(rng() * stations.length)];
            if (!result.includes(station)) {
                result.push(station);
            }
        }
        
        return result;
    }

    /**
     * Gets mock adjacent satellites
     * @param {string} satNumber - The satellite number
     * @param {Function} rng - Random number generator
     * @returns {Array<string>} Array of adjacent satellite IDs
     */
    getMockAdjacentSatellites(satNumber, rng) {
        const num = parseInt(satNumber) || 0;
        const candidates = [
            `Satellite/ ${num + 1}`,
            `Satellite/ ${num - 1}`,
            `Satellite/ ${num + 10}`,
            `Satellite/ ${num - 10}`
        ];
        
        return candidates.filter(() => rng() > 0.4);
    }

    /**
     * Initializes the widget
     */
    init() {
        console.log('SatelliteInfo widget initialized - displaying mock data');
    }
}

export { SatelliteInfo }; 