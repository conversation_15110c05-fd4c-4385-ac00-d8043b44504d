import {basisBackendUrl, GET, POST } from "../components/utils/api/basicURL";
import {getConstellationList, getTerminalList, getStatus, getCZMLData} from "../components/utils/api/getListAPI";

import { setSelectedConstellation, getTerminalPathCzml } from "../components/utils/api/postAPI";
import { useEffect, useState } from "react";

const getConstellationCzml = basisBackendUrl + "/get_czml"

// http://10.176.26.55:5001/api/get_constellation
// http://10.176.26.55:5001/api/get_terminal
// http://10.176.26.55:5001/api/get_status
// http://10.176.26.55:5001/api/get_ns3_state

interface TestResults {
    constellation: string[] | null;
    terminal: string[] | null;
    status: any | null;
    error: string | null;
    czmlData: any | null;
    satelliteCzmlSaved: boolean;
    groundStationCzmlSaved: boolean;
    terminalPathCzmlData: any | null;
    terminalPathCzmlSaved: boolean;
}

const TestComponent = () => {
    const [testResults, setTestResults] = useState<TestResults>({
        constellation: null,
        terminal: null,
        status: null,
        error: null,
        czmlData: null,
        satelliteCzmlSaved: false,
        groundStationCzmlSaved: false,
        terminalPathCzmlData: null,
        terminalPathCzmlSaved: false
    });

    useEffect(() => {
        const runTests = async () => {
            try {
                console.log('开始测试 API 函数...');
                
                // 测试 getConstellationList
                console.log('测试 getConstellationList...');
                const constellationData = await getConstellationList();
                console.log('星座列表数据:', constellationData);
                
                // 测试 getTerminalList
                console.log('测试 getTerminalList...');
                const terminalData = await getTerminalList();
                console.log('终端列表数据:', terminalData);
                
                // 测试 getStatus
                console.log('测试 getStatus...');
                const statusData = await getStatus();
                console.log('状态数据:', statusData);

                setTestResults({
                    constellation: constellationData,
                    terminal: terminalData,
                    status: statusData,
                    error: null,
                    czmlData: null,
                    satelliteCzmlSaved: false,
                    groundStationCzmlSaved: false,
                    terminalPathCzmlData: null,
                    terminalPathCzmlSaved: false
                });
            } catch (error: unknown) {
                console.error('测试过程中发生错误:', error);
                setTestResults(prev => ({
                    ...prev,
                    error: error instanceof Error ? error.message : '未知错误'
                }));
            }
        };

        runTests();
    }, []);

    // 保存CZML文件的函数
    const saveCzmlFile = (data: any, fileType: string) => {
        // 创建Blob对象
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                
        // 创建下载链接
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `${fileType}_${new Date().getTime()}.czml`;
        
        // 模拟点击下载
        document.body.appendChild(link);
        link.click();
        
        // 清理
        URL.revokeObjectURL(url);
        document.body.removeChild(link);
        
        console.log(`${fileType} CZML数据已保存为文件`);
    };

    // 测试新增函数并保存CZML文件
    const testNewFunctionsAndSave = async () => {
        try {
            // 先运行 setSelectedConstellation
            console.log('测试 setSelectedConstellation(2)...');
            const setResult = await setSelectedConstellation(2);
            console.log('设置星座结果:', setResult);
            
            // 等待 300ms
            console.log('等待300ms...');
            await new Promise(resolve => setTimeout(resolve, 300));
            
            // 然后运行 getCZMLData
            console.log('测试 getCZMLData()...');
            const czmlData = await getCZMLData();
            
            // 更新结果状态显示CZML数据
            setTestResults(prev => ({
                ...prev,
                czmlData: czmlData
            }));
            console.log(czmlData)
            // 检查CZML数据并保存分别保存索引0和1的部分
            if (czmlData) {
                // 保存卫星CZML数据 (索引0)
                if (czmlData.czml_data[0]) {
                    console.log(czmlData.czml_data[0])
                    saveCzmlFile(czmlData.czml_data[0], 'satellite');
                    setTestResults(prev => ({
                        ...prev,
                        satelliteCzmlSaved: true
                    }));
                }
                
                // 保存地面站CZML数据 (索引1)
                if (czmlData.czml_data[1]) {
                    console.log(czmlData.czml_data[1])
                    saveCzmlFile(czmlData.czml_data[1], 'ground_station');
                    setTestResults(prev => ({
                        ...prev,
                        groundStationCzmlSaved: true
                    }));
                }
            }
            
            console.log('新函数测试完成');
        } catch (error: unknown) {
            console.error('测试新函数过程中发生错误:', error);
            setTestResults(prev => ({
                ...prev,
                error: error instanceof Error ? error.message : '未知错误'
            }));
        }
    };

    // 测试终端路径CZML函数
    const testTerminalPathCzml = async () => {
        try {
            console.log('测试 getTerminalPathCzml(0, 1)...');
            const pathCzmlData = await getTerminalPathCzml(0, 1);
            console.log('终端路径CZML数据:', pathCzmlData);
            
            // 更新结果状态显示终端路径CZML数据
            setTestResults(prev => ({
                ...prev,
                terminalPathCzmlData: pathCzmlData
            }));
            
            // 保存终端路径CZML数据
            if (pathCzmlData) {
                saveCzmlFile(pathCzmlData, 'terminal_path');
                setTestResults(prev => ({
                    ...prev,
                    terminalPathCzmlSaved: true
                }));
            }
            
            console.log('终端路径测试完成');
        } catch (error: unknown) {
            console.error('测试终端路径过程中发生错误:', error);
            setTestResults(prev => ({
                ...prev,
                error: error instanceof Error ? error.message : '未知错误'
            }));
        }
    };

    return (
        <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
            <h2 style={{ color: '#1890ff' }}>API 测试结果</h2>
            {testResults.error ? (
                <div style={{ color: 'red', padding: '10px', border: '1px solid red', borderRadius: '4px', backgroundColor: '#fff1f0' }}>
                    <h3>错误信息:</h3>
                    <p>{testResults.error}</p>
                </div>
            ) : (
                <>
                    <div style={{ marginBottom: '20px' }}>
                        <h3>星座列表数据:</h3>
                        <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '4px', maxHeight: '200px', overflow: 'auto' }}>{JSON.stringify(testResults.constellation, null, 2)}</pre>
                    </div>
                    <div style={{ marginBottom: '20px' }}>
                        <h3>终端列表数据:</h3>
                        <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '4px', maxHeight: '200px', overflow: 'auto' }}>{JSON.stringify(testResults.terminal, null, 2)}</pre>
                    </div>
                    <div style={{ marginBottom: '20px' }}>
                        <h3>状态数据:</h3>
                        <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '4px', maxHeight: '200px', overflow: 'auto' }}>{JSON.stringify(testResults.status, null, 2)}</pre>
                    </div>
                    
                    {testResults.czmlData && (
                        <div style={{ marginBottom: '20px' }}>
                            <h3>CZML数据:</h3>
                            <div style={{ display: 'flex', gap: '20px' }}>
                                <div style={{ flex: 1 }}>
                                    <h4>卫星CZML数据 (索引0):</h4>
                                    <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '4px', maxHeight: '300px', overflow: 'auto' }}>{JSON.stringify(testResults.czmlData[0], null, 2)}</pre>
                                </div>
                                <div style={{ flex: 1 }}>
                                    <h4>地面站CZML数据 (索引1):</h4>
                                    <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '4px', maxHeight: '300px', overflow: 'auto' }}>{JSON.stringify(testResults.czmlData[1], null, 2)}</pre>
                                </div>
                            </div>
                        </div>
                    )}
                    
                    {testResults.terminalPathCzmlData && (
                        <div style={{ marginBottom: '20px' }}>
                            <h3>终端路径CZML数据:</h3>
                            <pre style={{ backgroundColor: '#f5f5f5', padding: '10px', borderRadius: '4px', maxHeight: '300px', overflow: 'auto' }}>{JSON.stringify(testResults.terminalPathCzmlData, null, 2)}</pre>
                        </div>
                    )}
                    
                    {(testResults.satelliteCzmlSaved || testResults.groundStationCzmlSaved || testResults.terminalPathCzmlSaved) && (
                        <div style={{ color: 'green', padding: '10px', border: '1px solid green', borderRadius: '4px', backgroundColor: '#f6ffed', marginBottom: '20px' }}>
                            <p>
                                {testResults.satelliteCzmlSaved && '✓ 卫星CZML数据已成功保存为文件！'}<br/>
                                {testResults.groundStationCzmlSaved && '✓ 地面站CZML数据已成功保存为文件！'}<br/>
                                {testResults.terminalPathCzmlSaved && '✓ 终端路径CZML数据已成功保存为文件！'}
                            </p>
                        </div>
                    )}
                </>
            )}
            
            <div style={{ marginTop: '20px', display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                <button 
                    onClick={testNewFunctionsAndSave}
                    style={{
                        padding: '10px 15px',
                        backgroundColor: '#1890ff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '16px',
                        boxShadow: '0 2px 5px rgba(0,0,0,0.2)'
                    }}
                >
                    测试新函数并保存卫星和地面站CZML数据
                </button>
                
                <button 
                    onClick={testTerminalPathCzml}
                    style={{
                        padding: '10px 15px',
                        backgroundColor: '#52c41a',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '16px',
                        boxShadow: '0 2px 5px rgba(0,0,0,0.2)'
                    }}
                >
                    测试终端路径CZML (终端A:0, 终端B:1)
                </button>
                
                <p style={{ color: '#888', fontSize: '14px', marginTop: '10px', width: '100%' }}>
                    绿色按钮将执行: getTerminalPathCzml(0, 1) → 保存终端路径CZML文件
                </p>
            </div>
        </div>
    );
};

export default TestComponent;