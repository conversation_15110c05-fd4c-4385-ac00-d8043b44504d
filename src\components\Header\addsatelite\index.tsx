import React, { useState } from 'react';
import { Modal, Tabs, Form, Input, Button, Upload, Select, InputNumber, message } from 'antd';
import { UploadOutlined, InboxOutlined } from '@ant-design/icons';
import type { TabsProps, UploadFile } from 'antd';
import styles from './style.module.css';
import {
  uploadSatelliteDataFiles,
  uploadSatelliteDataJson,
  validateJsonData,
  scheduleTleUpdate,
  cancelTleUpdate,
  type SatelliteConstellationData
} from './uploadAPI';

const { TabPane } = Tabs;
const { Dragger } = Upload;
const { Option } = Select;

interface AddSatelliteProps {
  visible: boolean;
  onClose: () => void;
  onSubmit: (data: any) => void;
}

interface FileImportData {
  constellationName: string;
  tleFile: UploadFile | null;
  islFile: UploadFile | null;
}

interface TemplateData {
  constellationName: string;
  orbitCount: number;
  satellitePerOrbit: number;
  altitude: number;
  inclination: number;
  meanMotion: number;
  beamRadius: number;
  islConfig: string;
}

interface ScheduledData {
  tleUrl: string;
  intervalInSeconds: number;
}

const AddSatellite: React.FC<AddSatelliteProps> = ({
  visible,
  onClose,
  onSubmit
}) => {
  const [activeTab, setActiveTab] = useState<string>('file');
  const [fileImportForm] = Form.useForm<FileImportData>();
  const [templateForm] = Form.useForm<TemplateData>();
  const [scheduledForm] = Form.useForm<ScheduledData>();
  const [tleFile, setTleFile] = useState<UploadFile | null>(null);
  const [islFile, setIslFile] = useState<UploadFile | null>(null);

  // 文件上传配置
  const uploadProps = {
    name: 'file',
    multiple: false,
    beforeUpload: (file: UploadFile) => {
      return false; // 阻止自动上传
    },
  };

  // TLE文件上传处理
  const handleTleUpload = (info: any) => {
    const { file } = info;
    if (file.name.endsWith('.tle') || file.name.endsWith('.txt')) {
      setTleFile(file);
      message.success(`${file.name} 文件上传成功`);
    } else {
      message.error('请上传 .tle 或 .txt 格式的文件');
    }
  };

  // ISL文件上传处理
  const handleIslUpload = (info: any) => {
    const { file } = info;
    if (file.name.endsWith('.isl') || file.name.endsWith('.txt') || file.name.endsWith('.json')) {
      setIslFile(file);
      message.success(`${file.name} 文件上传成功`);
    } else {
      message.error('请上传 .isl, .txt 或 .json 格式的文件');
    }
  };

  // 文件导入提交
  const handleFileImportSubmit = async (values: FileImportData) => {
    if (!tleFile ) {
      message.error('请上传所有必需的文件');
      return;
    }

    // 构建FormData
    const formData = new FormData();
    formData.append('tles', tleFile as any);
    formData.append('isls', islFile as any);

    try {
      message.loading('正在上传文件...', 0);
      
      const result = await uploadSatelliteDataFiles(formData);
      
      message.destroy(); // 清除loading消息
      
      if (result.success) {
        message.success(result.message || '文件上传成功');
        onSubmit({
          type: 'file',
          success: true,
          data: result
        });
        handleClose();
      } else {
        message.error(result.error || '文件上传失败');
      }
    } catch (error: any) {
      message.destroy();
      message.error(`上传失败: ${error.message}`);
      console.error('文件上传错误:', error);
    }
  };

  // 模版构建提交
  const handleTemplateSubmit = async (values: TemplateData) => {
    // 将表单数据转换为API期望的格式
    const apiData: SatelliteConstellationData = {
      constellation_name: values.constellationName,
      NUM_ORBS: values.orbitCount,
      NUM_SATS_PER_ORB: values.satellitePerOrbit,
      ALTITUDE_M: values.altitude,
      INCLINATION_DEGREE: values.inclination,
      MEAN_MOTION_REV_PER_DAY: values.meanMotion || 15.19, // 默认值
      SATELLITE_CONE_RADIUS_M: values.beamRadius || 940700, // 默认值
      isl_config: values.islConfig
    };

    // 验证数据
    const validation = validateJsonData(apiData);
    if (!validation.isValid) {
      message.error(validation.error);
      return;
    }

    try {
      message.loading('正在创建星座配置...', 0);
      
      const result = await uploadSatelliteDataJson(apiData);
      
      message.destroy(); // 清除loading消息
      
      if (result.success) {
        message.success(result.message || '星座配置创建成功');
        onSubmit({
          type: 'template',
          success: true,
          data: result,
          config: apiData
        });
        handleClose();
      } else {
        message.error(result.error || '星座配置创建失败');
      }
    } catch (error: any) {
      message.destroy();
      message.error(`创建失败: ${error.message}`);
      console.error('模版构建错误:', error);
    }
  };

  // 定时接收提交
  const handleScheduledSubmit = async (values: ScheduledData) => {
    try {
      message.loading('正在设置定时任务...', 0);

      const result = await scheduleTleUpdate(values.tleUrl, values.intervalInSeconds);

      message.destroy(); // 清除loading消息

      if (result.success) {
        message.success(result.message || '定时任务设置成功');
        onSubmit({
          type: 'scheduled',
          success: true,
          data: result
        });
        handleClose();
      } else {
        message.error(result.error || '定时任务设置失败');
      }
    } catch (error: any) {
      message.destroy();
      message.error(`设置失败: ${error.message}`);
      console.error('定时任务设置错误:', error);
    }
  };

  // 取消定时任务
  const handleCancelScheduled = async () => {
    try {
      message.loading('正在取消定时任务...', 0);

      const result = await cancelTleUpdate();

      message.destroy(); // 清除loading消息

      if (result.success) {
        message.success(result.message || '定时任务取消成功');
      } else {
        message.error(result.error || '定时任务取消失败');
      }
    } catch (error: any) {
      message.destroy();
      message.error(`取消失败: ${error.message}`);
      console.error('取消定时任务错误:', error);
    }
  };

  // 关闭处理
  const handleClose = () => {
    fileImportForm.resetFields();
    templateForm.resetFields();
    scheduledForm.resetFields();
    setTleFile(null);
    setIslFile(null);
    setActiveTab('file');
    onClose();
  };

  // ISL配置选项
  const islConfigOptions = [
    { value: 'isls_plus_grid', label: 'ISLs Plus Grid' },
    { value: 'isls_none', label: '无ISL配置' },
    { value: 'isls_six_grid', label: 'ISLs六网格' },
    { value: 'isls_bridge', label: 'ISLs桥接' }
  ];

  return (
    <Modal
      title="星座设计"
      visible={visible}
      onCancel={handleClose}
      footer={null}
      width={600}
      centered
      className={styles.addSatelliteModal}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="星座文件导入" key="file">
          <Form
            form={fileImportForm}
            layout="vertical"
            onFinish={handleFileImportSubmit}
            className={styles.form}
          >
            {/* <Form.Item
              name="constellationName"
              label="星座名称"
              rules={[{ required: true, message: '请输入星座名称' }]}
            >
              <Input placeholder="请输入星座名称" />
            </Form.Item> */}

            <Form.Item
              label="TLE文件"
              rules={[{ required: true, message: '请上传TLE文件' }]}
            >
              <Dragger
                {...uploadProps}
                onChange={handleTleUpload}
                accept=".tle,.txt"
                className={styles.uploader}
              >
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">
                  {tleFile ? tleFile.name : '点击或拖拽TLE文件到此区域'}
                </p>
                <p className="ant-upload-hint">
                  支持 .tle 和 .txt 格式文件
                </p>
              </Dragger>
            </Form.Item>

            <Form.Item
              label="ISL文件"
              rules={[{ required: false, message: '请上传ISL文件' }]}
            >
              <Dragger
                {...uploadProps}
                onChange={handleIslUpload}
                accept=".isl,.txt,.json"
                className={styles.uploader}
              >
                <p className="ant-upload-drag-icon">
                  <InboxOutlined />
                </p>
                <p className="ant-upload-text">
                  {islFile ? islFile.name : '点击或拖拽ISL文件到此区域'}
                </p>
                <p className="ant-upload-hint">
                  支持 .isl, .txt 和 .json 格式文件
                </p>
              </Dragger>
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" className={styles.submitButton}>
                导入星座
              </Button>
            </Form.Item>
          </Form>
        </TabPane>

        <TabPane tab="星座模版构建" key="template">
          <Form
            form={templateForm}
            layout="vertical"
            onFinish={handleTemplateSubmit}
            className={styles.form}
          >
            <Form.Item
              name="constellationName"
              label="星座名称"
              rules={[{ required: true, message: '请输入星座名称' }]}
            >
              <Input placeholder="请输入星座名称" />
            </Form.Item>

            <Form.Item
              name="orbitCount"
              label="轨道数量"
              rules={[{ required: true, message: '请输入轨道数量' }]}
            >
              <InputNumber
                min={1}
                max={100}
                placeholder="请输入轨道数量"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name="satellitePerOrbit"
              label="每个轨道的卫星数"
              rules={[{ required: true, message: '请输入每个轨道的卫星数' }]}
            >
              <InputNumber
                min={1}
                max={1000}
                placeholder="请输入每个轨道的卫星数"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name="altitude"
              label="海拔高度（m）"
              rules={[{ required: true, message: '请输入海拔高度' }]}
            >
              <InputNumber
                min={200000}
                max={2000000}
                placeholder="请输入海拔高度（米）"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name="inclination"
              label="倾斜度（度）"
              rules={[{ required: true, message: '请输入倾斜度' }]}
            >
              <InputNumber
                min={0}
                max={180}
                step={0.1}
                placeholder="请输入倾斜度（度）"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name="meanMotion"
              label="平均运动（转/天）"
            >
              <InputNumber
                min={1}
                max={20}
                step={0.01}
                placeholder="请输入平均运动（转/天）"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name="beamRadius"
              label="卫星锥体半径（m）"
            >
              <InputNumber
                min={100000}
                max={2000000}
                placeholder="请输入卫星锥体半径（米）"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name="islConfig"
              label="路径/ISL配置"
              rules={[{ required: true, message: '请选择ISL配置' }]}
            >
              <Select placeholder="请选择ISL配置">
                {islConfigOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" className={styles.submitButton}>
                创建星座
              </Button>
            </Form.Item>
          </Form>
        </TabPane>

        <TabPane tab="定时接收" key="scheduled">
          <Form
            form={scheduledForm}
            layout="vertical"
            onFinish={handleScheduledSubmit}
            className={styles.form}
          >
            <Form.Item
              name="tleUrl"
              label="TLE数据源URL"
              rules={[
                { required: true, message: '请输入TLE数据源URL' },
                { type: 'url', message: '请输入有效的URL地址' }
              ]}
            >
              <Input
                placeholder="请输入TLE数据源URL，例如：https://example.com/tle/data.tle"
                style={{
                  width: '100%',
                  backgroundColor: '#2a2a2a',
                  border: '1px solid #444',
                  color: '#fff'
                }}
              />
            </Form.Item>

            <Form.Item
              name="intervalInSeconds"
              label="更新间隔 (秒)"
              rules={[{ required: true, message: '请输入更新间隔' }]}
            >
              <InputNumber
                min={60}
                max={86400}
                placeholder="请输入更新间隔（秒）"
                style={{
                  width: '100%',
                  backgroundColor: '#2a2a2a',
                  border: '1px solid #444',
                  color: '#fff'
                }}
              />
            </Form.Item>

            <Form.Item>
              <div style={{ display: 'flex', gap: '12px' }}>
                <Button
                  type="primary"
                  htmlType="submit"
                  className={styles.submitButton}
                  style={{ flex: 1 }}
                >
                  定时上传
                </Button>
                <Button
                  type="primary"
                  onClick={handleCancelScheduled}
                  className={styles.submitButton}
                  style={{ flex: 1 }}
                >
                  取消任务
                </Button>
              </div>
            </Form.Item>
          </Form>
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default AddSatellite; 