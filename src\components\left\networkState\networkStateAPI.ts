const UPDATE_FREQUENCY = 200; // 毫秒 
const ipAddress = "************";
const BackendUrl = "http://" + ipAddress + ":5000/api/get_e2e_status";

export function getE2EStatus(): Promise<{latency: number, packet_loss_rate: number}> {
  return fetch(BackendUrl)
    .then(res => res.json())
    .then(res => {
      if (res.status === 200 && res.data) {
        return {
          latency: res.data.latency,
          packet_loss_rate: res.data.packet_loss_rate
        };
      } else {
        return Promise.reject(new Error("Invalid response"));
      }
    });
}

export { UPDATE_FREQUENCY, BackendUrl };